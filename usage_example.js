/**
 * 优化版 handleSession 的使用示例和配置
 * 
 * 这个文件展示了如何集成和使用优化版本的 handleSession 函数
 */

import { handleSession } from './optimized_handleSession.js';

// 从原始 _worker.js 导入必要的配置和函数
// 注意：这些需要根据实际的 _worker.js 文件进行调整

/**
 * 全局配置 - 从 _worker.js 复制
 */
const globalSessionConfig = {
    connect: {
        connectMode: 'direct',
        retryMode: 'relayip',
    },
    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43',
        pass: 'a233255z',
        sha224: '419023a775279d21cdbda41971c0bb52e962f11b4f4bfba6015a268b',
    },
    relay: {
        ip: 'jp.almain126.changeip.biz',
        _port: null,
        get port() {
            if (this._port !== null) {
                return this._port;
            }
            return this.ip.includes(':') ? this.ip.split(':')[1] : (null || undefined);
        },
        set port(value) {
            this._port = value;
        },
        socks: 'web5.serv00.com:13668',
    },
    misc: {
        subName: 'CF-Workers-SUB'
    }
};

const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip',
    targetProtocolType0: 'vless',
    targetProtocolType1: 'trojan',
    targetPathType0: 'vl',
    targetPathType1: 'tr'
};

/**
 * 主要的 Worker 入口点 - 集成优化版本
 */
export default {
    async fetch(request, env, ctx) {
        try {
            // 环境变量配置
            const { CONNECT_MODE, RETRY_MODE, USER_GUID, USER_PASS, USER_SHA224, RELAY_IP, RELAY_SOCKS } = env;
            
            // 更新全局配置
            globalControllerConfig.connectMode = (CONNECT_MODE || globalSessionConfig.connect.connectMode).toLowerCase();
            globalControllerConfig.retryMode = (RETRY_MODE || globalSessionConfig.connect.retryMode).toLowerCase();
            
            globalSessionConfig.user.id = USER_GUID || globalSessionConfig.user.id;
            globalSessionConfig.user.pass = USER_PASS || globalSessionConfig.user.pass;
            globalSessionConfig.user.sha224 = USER_SHA224 || globalSessionConfig.user.sha224;
            globalSessionConfig.relay.ip = RELAY_IP || globalSessionConfig.relay.ip;
            globalSessionConfig.relay.socks = RELAY_SOCKS || globalSessionConfig.relay.socks;

            const url = new URL(request.url);
            const upgradeHeader = request.headers.get('Upgrade');

            // 处理非 WebSocket 请求
            if (!upgradeHeader || upgradeHeader !== 'websocket') {
                return handleHttpRequests(url, request);
            }

            // 处理 WebSocket 升级请求
            return await handleWebSocketUpgrade(request, env, ctx, url);

        } catch (error) {
            console.error('Worker error:', error);
            return new Response(`Worker Error: ${error.message}`, { status: 500 });
        }
    }
};

/**
 * 处理 HTTP 请求
 */
function handleHttpRequests(url, request) {
    switch (url.pathname) {
        case '/':
            return new Response(null, { status: 204 });
        case '/health':
            return new Response(JSON.stringify({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: '2.0-optimized'
            }), {
                headers: { 'Content-Type': 'application/json' }
            });
        default:
            return new Response('Not Found', { status: 404 });
    }
}

/**
 * 处理 WebSocket 升级请求
 */
async function handleWebSocketUpgrade(request, env, ctx, url) {
    // 解析路径以确定协议类型
    const pathType = url.pathname.split('/')[1];
    let protocolMode;
    
    switch (pathType) {
        case globalControllerConfig.targetPathType1:
            protocolMode = globalControllerConfig.targetProtocolType1;
            break;
        case globalControllerConfig.targetPathType0:
        default:
            protocolMode = globalControllerConfig.targetProtocolType0;
            break;
    }

    // 使用优化版本的 handleSession
    return await handleSession(request, env, ctx, protocolMode);
}

/**
 * 性能监控中间件
 */
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            totalRequests: 0,
            websocketConnections: 0,
            errors: 0,
            averageResponseTime: 0
        };
    }

    recordRequest(startTime) {
        this.metrics.totalRequests++;
        const responseTime = performance.now() - startTime;
        this.metrics.averageResponseTime = 
            (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime) / 
            this.metrics.totalRequests;
    }

    recordWebSocketConnection() {
        this.metrics.websocketConnections++;
    }

    recordError() {
        this.metrics.errors++;
    }

    getMetrics() {
        return { ...this.metrics };
    }
}

// 全局性能监控实例
const performanceMonitor = new PerformanceMonitor();

/**
 * 配置建议和最佳实践
 */
const OPTIMIZATION_RECOMMENDATIONS = {
    // 生产环境配置
    production: {
        enableMetrics: false,  // 生产环境关闭详细指标以提高性能
        bufferSize: 128 * 1024, // 增大缓冲区
        connectionTimeout: 60000, // 增加超时时间
        maxRetries: 3
    },
    
    // 开发环境配置
    development: {
        enableMetrics: true,   // 开发环境启用详细指标
        bufferSize: 32 * 1024, // 较小缓冲区便于调试
        connectionTimeout: 15000,
        maxRetries: 1
    },
    
    // 高并发环境配置
    highConcurrency: {
        enableMetrics: false,
        bufferSize: 256 * 1024, // 更大缓冲区
        connectionTimeout: 30000,
        maxRetries: 2,
        enableConnectionPool: true // 启用连接池
    }
};

/**
 * 部署检查清单
 */
const DEPLOYMENT_CHECKLIST = {
    required: [
        '✓ 更新 globalSessionConfig 中的用户 ID 和密钥',
        '✓ 配置正确的中继服务器地址',
        '✓ 设置适当的环境变量',
        '✓ 测试 WebSocket 连接功能',
        '✓ 验证协议解析正确性'
    ],
    
    optional: [
        '○ 启用性能监控',
        '○ 配置 SOCKS5 代理',
        '○ 设置连接池',
        '○ 添加自定义错误处理',
        '○ 实施速率限制'
    ],
    
    performance: [
        '⚡ 根据负载调整缓冲区大小',
        '⚡ 优化连接超时设置',
        '⚡ 监控内存使用情况',
        '⚡ 测试并发连接处理能力',
        '⚡ 验证错误恢复机制'
    ]
};

/**
 * 使用说明
 */
const USAGE_INSTRUCTIONS = `
优化版 handleSession 使用指南：

1. 基本集成：
   - 导入 handleSession 函数
   - 配置全局变量
   - 在 WebSocket 升级时调用

2. 性能优化：
   - 根据环境选择合适的配置
   - 监控关键性能指标
   - 定期检查资源使用情况

3. 错误处理：
   - 查看控制台日志获取详细错误信息
   - 使用内置的重试机制
   - 实施适当的降级策略

4. 监控和调试：
   - 启用性能指标收集
   - 使用 /health 端点检查状态
   - 分析连接和传输统计

5. 扩展功能：
   - 添加自定义协议支持
   - 实现连接池管理
   - 集成外部监控系统
`;

// 导出配置供外部使用
export { 
    globalSessionConfig, 
    globalControllerConfig, 
    OPTIMIZATION_RECOMMENDATIONS,
    DEPLOYMENT_CHECKLIST,
    USAGE_INSTRUCTIONS,
    performanceMonitor
};
